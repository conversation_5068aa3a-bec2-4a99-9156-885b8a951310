#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from sqlalchemy import select, update

from engine.rdb import g, session_maker_sync, query_order, paginator, fetch_all
from common.time import now_datetime
from model.auth import TenantModel
from model.plan import TenantPlanModel, PlanModel
from controller.admin.plan import Plan, RefreshType


class TenantController:
    @staticmethod
    def get_query(match: str = None, order_by: str = None):
        where = [TenantModel.is_delete == 0]
        if match:
            where.append(TenantModel.name.ilike(f"%{match}%"))

        query = (
            select(
                TenantModel.id.label("tenant_id"),
                TenantModel.name,
                TenantModel.create_time
            )
            .where(*where)
        )

        query = query_order(query=query, order_by=order_by, table=TenantModel)

        return query

    async def get_list(self, match: str = None, page: int = 1, per_page: int = 20, order_by: str = None):
        query = self.get_query(match=match, order_by=order_by)
        pager, tenants = await paginator(query=query, page=page, per_page=per_page)
        return pager, tenants

    @staticmethod
    async def create(name: str, plan_id: int):
        plan = await Plan.get_tenant_one(plan_id)
        if not plan:
            raise ValueError(f"套餐不存在")

        tenant = TenantModel(name=name)
        g.session.add(tenant)
        await g.session.flush()

        now_time = now_datetime()
        expire_time = None
        if plan["refresh_type"] == RefreshType.period and plan["refresh_count"] > 0:
            expire_time = now_time + datetime.timedelta(days=plan["refresh_period_days"]) * plan["refresh_count"]

        tenant_plan = TenantPlanModel(
            tenant_id=tenant.id, plan_id=plan_id, token_limit=plan["tokens"], expire_time=expire_time,
            refresh_time=now_time+datetime.timedelta(days=plan["refresh_period_days"]),
            remain_refresh_count=plan["refresh_count"]-1)
        g.session.add(tenant_plan)
        await g.session.flush()

        return tenant.id

    @staticmethod
    async def update(tenant_id: int, name: str = None):
        update_info = {}
        if name is not None:
            update_info[TenantModel.name] = name

        query = (update(TenantModel)
                 .where(TenantModel.id == tenant_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(tenant_id: int):
        query = (update(TenantModel)
                 .where(TenantModel.id == tenant_id)
                 .values({TenantModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def get_plan_query(tenant_id: int = None, order_by: str = None):
        where = [
            TenantPlanModel.is_delete == 0,
            TenantModel.is_delete == 0,
        ]
        if tenant_id:
            where.append(TenantPlanModel.tenant_id == tenant_id)

        query = (
            select(
                TenantPlanModel.id.label("tenant_plan_id"),
                TenantPlanModel.tenant_id,
                TenantPlanModel.plan_id,
                TenantPlanModel.token_limit,
                TenantPlanModel.token_used,
                TenantPlanModel.expire_time,
                TenantPlanModel.refresh_time,
                TenantPlanModel.remain_refresh_count,
                TenantPlanModel.create_time,
                TenantModel.name.label("tenant_name"),
                PlanModel.name.label("plan_name"),
                PlanModel.refresh_type,
                PlanModel.refresh_period_days,
                PlanModel.refresh_count,
            )
            .join(TenantModel, TenantPlanModel.tenant_id == TenantModel.id)
            .join(PlanModel, TenantPlanModel.plan_id == PlanModel.id)
            .where(*where)
        )

        query = query_order(query=query, order_by=order_by, table=TenantPlanModel)

        return query

    @staticmethod
    async def get_plan_list(tenant_id: int = None, page: int = 1, per_page: int = 20, order_by: str = None):
        query = TenantController.get_plan_query(tenant_id=tenant_id, order_by=order_by)
        return await paginator(query=query, page=page, per_page=per_page)

    @staticmethod
    async def get_plan_all(tenant_id: int):
        query = TenantController.get_plan_query(tenant_id=tenant_id)
        return await fetch_all(query=query)

    @staticmethod
    async def create_plan(tenant_id: int, plan_id: int):
        plan = await Plan.get_tenant_one(plan_id)
        if not plan:
            raise ValueError(f"套餐不存在")

        now_time = now_datetime()
        expire_time = None
        if plan["refresh_type"] == RefreshType.period and plan["refresh_count"] > 0:
            expire_time = now_time + datetime.timedelta(days=plan["refresh_period_days"]) * plan["refresh_count"]

        tenant_plan = TenantPlanModel(
            tenant_id=tenant_id, plan_id=plan_id, token_limit=plan["tokens"], expire_time=expire_time,
            refresh_time=now_time+datetime.timedelta(days=plan["refresh_period_days"]),
            remain_refresh_count=plan["refresh_count"]-1)
        g.session.add(tenant_plan)
        await g.session.flush()

        return tenant_plan.id

    @staticmethod
    async def delete_plan(tenant_plan_id: int):
        query = (update(TenantPlanModel)
                 .where(TenantPlanModel.id == tenant_plan_id)
                 .values({TenantPlanModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            tenant = TenantModel(id=1, name="默认租户")
            session.add(tenant)

            tenant_plan = TenantPlanModel(tenant_id=1, plan_id=1, token_limit=0)
            session.add(tenant_plan)
            session.commit()


Tenant = TenantController()
