#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from sqlalchemy import select, update, or_, exists

from model.auth import UserModel, RoleModel, TenantModel
from engine.rdb import g, query_order, fetch_one, fetch_all, paginator, session_maker_sync
from common.sm import gm_sm2
from common.time import now_datetime
from exception.custom_exception import PasswordError, ExpireTokenError
from controller.admin.role import Role


class UserController:
    @staticmethod
    def get_query(user_id: int = None, user_ids: list[int] = None, username: str = None, password: str = None,
                  match: str = None, order_by: str = None, is_delete: bool = False):
        where = []
        if g.tenant_id is not None:
            where.append(RoleModel.tenant_id == g.tenant_id)
        if user_id is not None:
            where.append(UserModel.id == user_id)
        if user_ids is not None:
            where.append(UserModel.id.in_(user_ids))
        if username:
            where.append(UserModel.username == username)
        if password:
            where.append(UserModel.password == password)
        if match:
            where.append(
                or_(
                    UserModel.nickname.ilike(f"%{match}%"),
                    UserModel.username.ilike(f"%{match}%"),
                    UserModel.email.ilike(f"%{match}%"),
                    UserModel.phone_number.ilike(f"%{match}%")
                ))
        if is_delete is False:
            where.append(UserModel.is_delete == 0)

        query = (
            select(
                UserModel.id.label("user_id"),
                UserModel.tenant_id,
                UserModel.username,
                UserModel.nickname,
                UserModel.role_ids,
                UserModel.email,
                UserModel.phone_number,
                UserModel.expire_time,
                UserModel.info,
                UserModel.create_time,
                TenantModel.name.label("tenant_name"))
            .join(TenantModel, UserModel.tenant_id == TenantModel.id)
            .where(*where)
        )
        
        query = query_order(query=query, order_by=order_by, table=UserModel)
        
        return query

    async def get_one(self, user_id: int):
        query = self.get_query(user_id=user_id)
        user = await fetch_one(query)

        return user
    
    @staticmethod
    async def check_username(username: str):
        """检查用户名是否已存在

        Args:
            username: 要检查的用户名

        Returns:
            bool: True表示用户名已存在，False表示用户名不存在
        """
        query = select(
            exists().where(
                UserModel.username == username,
                UserModel.is_delete == 0
            )
        )
        result = await fetch_one(query)
        # fetch_one 返回字典，需要获取第一个值（布尔结果）
        return list(result.values())[0] if result else False

    async def get_list(self, user_id: int = None, user_ids: list[int] = None, match: str = None, page: int = 1,
                       per_page: int = 20, order_by: str = None):
        query = self.get_query(user_id=user_id, user_ids=user_ids, match=match, order_by=order_by)
        pager, users = await paginator(query=query, page=page, per_page=per_page)

        await self.get_user_roles(items=users)

        return pager, users

    async def get_all(self, user_ids: list[int]):
        query = self.get_query(user_ids=user_ids)
        users = await fetch_all(query=query)

        await self.get_user_roles(items=users)

        return users

    async def get_login_user(self, username:  str, password: str):
        query = self.get_query(username=username, password=password)

        user = await fetch_one(query)

        if not user:
            raise PasswordError(f"账号或密码错误")
        if user["expire_time"] and user["expire_time"] < now_datetime():
            raise ExpireTokenError("账户已到期,请联系管理员或商务")

        query = Role.get_query(role_ids=user["role_ids"])
        roles_mapping = {r["role_id"]: r for r in await fetch_all(query=query)}
        user["roles"] = [roles_mapping[r_id] for r_id in user["role_ids"] if r_id in roles_mapping]

        return user

    @staticmethod
    async def get_user_roles(items: list[dict]):
        role_ids = list(dict.fromkeys([r_id for item in items for r_id in item["role_ids"]]))
        query = Role.get_query(role_ids=role_ids)
        roles_mapping = {r["role_id"]: r for r in await fetch_all(query=query)}
        for item in items:
            item["roles"] = [roles_mapping[r_id] for r_id in item["role_ids"] if r_id in roles_mapping]

    @staticmethod
    async def create(username: str, password: str, nickname: str, role_ids: list[int], tenant_id: int = None, 
                     email: str = None, phone_number: str = None, expire_time: datetime.datetime = None, 
                     info: dict = None) -> UserModel:
        user = UserModel(
            username=username, password=password, nickname=nickname, role_ids=role_ids, tenant_id=tenant_id, 
            email=email, phone_number=phone_number, expire_time=expire_time, info=info)
        g.session.add(user)

        return user

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            user = UserModel(
                id=1, username="admin", tenant_id=1, password=gm_sm2.encrypt(plaintext="Gather1Stars"),
                nickname="超级管理员", role_ids=[1])
            session.add(user)
            session.commit()

    @staticmethod
    async def update(user_id: int, username: str = None, password: str = None, nickname: str = None,
                     role_ids: list[int] = None, email: str = None, phone_number: str = None,
                     expire_time: datetime.datetime = None, info: dict = None):
        update_info = {}
        update_info[UserModel.expire_time] = expire_time
        if username:
            update_info[UserModel.username] = username
        if password is not None:
            update_info[UserModel.password] = password
        if nickname:
            update_info[UserModel.nickname] = nickname
        if role_ids:
            update_info[UserModel.role_ids] = role_ids
        if email is not None:
            update_info[UserModel.email] = email
        if phone_number is not None:
            update_info[UserModel.phone_number] = phone_number
        if info is not None:
            update_info[UserModel.info] = info

        query = (update(UserModel)
                 .where(UserModel.id == user_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(user_ids: list[int]):
        query = (update(UserModel)
                 .where(UserModel.id.in_(user_ids))
                 .values({UserModel.is_delete: 1}))
        await g.session.execute(query)


User = UserController()
