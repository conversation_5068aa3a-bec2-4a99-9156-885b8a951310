#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum

from sqlalchemy import select, func

from engine.rdb import g, fetch_one
from model.token_counts import TokenCountsModel


class LLMBusiness(StrEnum):
    doc_extract = "doc_extract"
    doc_parser = "doc_parser"
    abstract = "abstract"
    analytics = "analytics"
    chat = "chat"


class TokenCountsController:
    @staticmethod
    def get_query(start: str = None, end: str = None, business: str = None, create_user_id: int = None):
        where = [TokenCountsModel.is_delete == 0]
        if g.tenant_id:
            where.append(TokenCountsModel.tenant_id == g.tenant_id)
        if start is not None:
            where.append(TokenCountsModel.create_time >= start)
        if end is not None:
            where.append(TokenCountsModel.create_time < end)
        if business is not None:
            where.append(TokenCountsModel.business == business)
        if create_user_id is not None:
            where.append(TokenCountsModel.create_user_id == create_user_id)

        query = (
            select(
                func.sum(TokenCountsModel.input_tokens).label("input_tokens"),
                func.sum(TokenCountsModel.output_tokens).label("output_tokens"),
                func.sum(TokenCountsModel.input_tokens + TokenCountsModel.output_tokens).label("total_tokens"))
            .where(*where))

        return query

    async def get_stats(self, start: str = None, end: str = None, business: str = None, create_user_id: int = None):
        query = self.get_query(start=start, end=end, business=business, create_user_id=create_user_id)
        return await fetch_one(query)

    @staticmethod
    async def create(create_user_id: int, model_name: str, business: LLMBusiness, input_tokens: int, output_tokens: int,
                     tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        token_counts = TokenCountsModel(
            create_user_id=create_user_id, tenant_id=tenant_id, model_name=model_name, business=business,
            input_tokens=input_tokens, output_tokens=output_tokens)
        g.session.add(token_counts)
        await g.session.flush()

        return token_counts.id


TokenCounts = TokenCountsController()
