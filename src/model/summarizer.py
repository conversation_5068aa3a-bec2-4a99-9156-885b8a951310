#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import String, Text
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel, g_attr


class SummarizerModel(BaseModel):
    __tablename__ = "summarizer"
    __comment__ = "摘要分析表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="摘要分析ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")
    name: Mapped[str] = mapped_column(String(30), nullable=False, comment="摘要分析名称")
    repo_ids: Mapped[list] = mapped_column(default=[], nullable=False, comment="知识库IDs")
    data_range: Mapped[str] = mapped_column(String(256), nullable=False, comment="数据范围")
    tags: Mapped[list] = mapped_column(default='[]', nullable=False, comment="标签")
    llm_models: Mapped[list] = mapped_column(nullable=True, comment="使用模型")
    prompt: Mapped[str] = mapped_column(String(50), nullable=False, comment="摘要方向")
    execution: Mapped[str] = mapped_column(String(16), nullable=False, comment="执行时间")


class SummarizerTagModel(BaseModel):
    __tablename__ = "summarizer_tag"
    __comment__ = "摘要分析标签关联表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="摘要分析标签关联ID")
    summarizer_id: Mapped[int] = mapped_column(nullable=False, comment="摘要分析ID")
    name: Mapped[str] = mapped_column(String(12), nullable=False, index=True, comment="摘要分析标签名称")


class SummarizerTaskState:
    create = 0
    running = 1
    failed = 2
    succeeded = 3


class SummarizerTaskModel(BaseModel):
    __tablename__ = "summarizer_task"
    __comment__ = "摘要分析任务表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="摘要结果ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")
    summarizer_id: Mapped[int] = mapped_column(nullable=False, comment="摘要分析ID")
    summarizer_name: Mapped[str] = mapped_column(String(50), nullable=False, comment="摘要分析名称")
    repo_ids: Mapped[list] = mapped_column(nullable=False, comment="关联知识库IDs")
    summarizer_config: Mapped[dict] = mapped_column(default='{}', nullable=False, comment="摘要分析配置")
    related_doc_ids: Mapped[list] = mapped_column(nullable=True, comment="相关文档IDs")
    state: Mapped[int] = mapped_column(nullable=False, default=SummarizerTaskState.create, comment="任务状态")
    message: Mapped[str] = mapped_column(String(256), nullable=True, comment="任务信息")
